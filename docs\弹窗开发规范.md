# 弹窗开发规范

## 概述

本文档定义了药店零售系统项目中所有弹窗组件的设计标准、UI样式规范和交互行为，确保整个系统的用户体验一致性。

## 设计原则

### 1. 一致性原则
- 所有弹窗必须遵循统一的视觉风格和交互模式
- 保持与项目整体蓝色主题的一致性
- 确保不同页面间的弹窗行为保持统一

### 2. 用户友好原则
- 提供清晰的视觉反馈
- 支持多种关闭方式（点击背景、ESC键、关闭按钮）
- 显示适当的加载状态和错误提示

### 3. 可访问性原则
- 支持键盘导航
- 提供适当的焦点管理
- 确保屏幕阅读器兼容性

## UI样式规范

### 主题色彩
- **主要文字颜色**: `text-blue-700` - 用于标题、重要信息
- **次要文字颜色**: `text-gray-600` - 用于描述性文字
- **背景遮罩**: `bg-black bg-opacity-50` - 半透明黑色背景
- **弹窗背景**: `bg-white` - 纯白色背景
- **边框圆角**: `rounded-xl` - 统一使用较大圆角

### 按钮样式规范
```css
/* 主要操作按钮（确认、保存等） */
.btn-primary {
  @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 
         focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors;
}

/* 次要操作按钮（取消等） */
.btn-secondary {
  @apply px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md 
         hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors;
}

/* 危险操作按钮（删除等） */
.btn-danger {
  @apply px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 
         focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors;
}
```

### 布局规范
- **最大宽度**: `max-w-md` (中等弹窗) 或 `max-w-2xl` (大型弹窗)
- **内边距**: `p-6` - 统一使用6单位内边距
- **按钮间距**: `space-x-3` - 按钮之间3单位间距
- **阴影效果**: `shadow-xl` - 使用较强阴影效果

## 弹窗类型和组件

### 1. 确认删除对话框 (ConfirmDeleteDialog)
**用途**: 用于删除操作的二次确认
**位置**: `app/components/ConfirmDeleteDialog.tsx`

**使用示例**:
```tsx
<ConfirmDeleteDialog
  isOpen={isDeleteConfirmOpen}
  onClose={() => setIsDeleteConfirmOpen(false)}
  onConfirm={handleDeleteConfirm}
  title="确认删除药品"
  message="确定要删除这个药品吗？"
  itemName={deletingItem?.name}
  confirmText="确认删除"
  cancelText="取消"
/>
```

### 2. 错误提示对话框 (ErrorDialog/ErrorModal)
**用途**: 显示错误信息和异常提示
**位置**: `app/components/ErrorModal.tsx`

**使用示例**:
```tsx
<ErrorModal
  isOpen={showErrorModal}
  onClose={() => setShowErrorModal(false)}
  title="操作失败"
  message={errorMessage}
  details={errorDetails}
/>
```

### 3. 成功提示对话框 (SuccessModal)
**用途**: 显示操作成功的反馈信息
**位置**: `app/components/SuccessModal.tsx`

**使用示例**:
```tsx
<SuccessModal
  isOpen={showSuccessModal}
  onClose={() => setShowSuccessModal(false)}
  title="操作成功"
  message={successMessage}
/>
```

### 4. 通用警告对话框 (AlertDialog)
**用途**: 显示各种类型的提示信息
**位置**: `app/components/AlertDialog.tsx`

**使用示例**:
```tsx
<AlertDialog
  isOpen={isAlertOpen}
  onClose={() => setIsAlertOpen(false)}
  message={alertMessage}
  type="warning" // 'error' | 'success' | 'warning' | 'info'
  autoClose={true}
  autoCloseDelay={3000}
/>
```

## 交互行为规范

### 打开行为
1. 弹窗应该从中心位置淡入显示
2. 背景遮罩同时淡入
3. 自动聚焦到第一个可交互元素
4. 阻止页面滚动

### 关闭行为
支持以下关闭方式：
1. **点击背景遮罩**: 点击弹窗外部区域关闭
2. **ESC键**: 按下ESC键关闭弹窗
3. **关闭按钮**: 点击右上角X按钮或取消按钮
4. **确认操作**: 完成操作后自动关闭

### 动画效果
- **进入动画**: `ease-out duration-300` 淡入+缩放
- **退出动画**: `ease-in duration-200` 淡出+缩放
- **背景遮罩**: 同步淡入淡出效果

## 状态管理规范

### 基本状态
每个弹窗组件应该管理以下基本状态：
```tsx
const [isOpen, setIsOpen] = useState(false);
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);
```

### 表单弹窗额外状态
```tsx
const [formData, setFormData] = useState<FormData>({});
const [errors, setErrors] = useState<Partial<FormData>>({});
const [isDirty, setIsDirty] = useState(false);
```

## 错误处理规范

### 错误显示优先级
1. **表单验证错误**: 在表单字段附近显示
2. **业务逻辑错误**: 使用ErrorModal显示
3. **网络错误**: 使用AlertDialog显示
4. **系统错误**: 使用ErrorModal显示详细信息

### 错误信息格式
```tsx
interface ErrorInfo {
  title: string;        // 错误标题
  message: string;      // 主要错误信息
  details?: string;     // 详细错误信息（可选）
  code?: string;        // 错误代码（可选）
}
```

## 开发最佳实践

### 1. 组件复用
- 优先使用现有的通用弹窗组件
- 避免为每个页面创建专用弹窗组件
- 通过props传递自定义内容和行为

### 2. 性能优化
- 使用条件渲染避免不必要的DOM创建
- 合理使用useCallback和useMemo优化渲染
- 避免在弹窗内部进行复杂计算

### 3. 测试要求
- 确保所有关闭方式都能正常工作
- 测试键盘导航和焦点管理
- 验证不同屏幕尺寸下的显示效果
- 测试加载状态和错误状态的显示

### 4. 代码示例
```tsx
// 标准弹窗组件结构
export default function CustomModal({ isOpen, onClose, children }) {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
      style={{ zIndex: 10000 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-xl shadow-lg max-w-md w-full mx-4">
        <div className="p-6">
          {children}
        </div>
      </div>
    </div>
  );
}
```

## 注意事项

1. **z-index管理**: 所有弹窗使用统一的z-index值（10000）
2. **移动端适配**: 确保在小屏幕设备上的可用性
3. **浏览器兼容性**: 避免使用浏览器原生的alert、confirm、prompt
4. **国际化支持**: 所有文本内容支持多语言切换
5. **主题一致性**: 严格遵循蓝色字体主题 (text-blue-700)

## 更新记录

- **2025-07-05**: 初始版本创建
- 定义了基本的弹窗开发规范和样式标准
- 建立了组件复用和交互行为的统一标准
