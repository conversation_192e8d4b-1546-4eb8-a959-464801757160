// 药品表单验证规则和工具函数

export interface MedicineFormData {
  name: string;
  generic_name: string;
  description: string;
  barcode: string;
  category_id: number;
  supplier_id?: number;
  manufacturer: string;
  approval_number: string;
  specification: string;
  dosage_form: string;
  price: number;
  cost_price: number;
  stock_quantity: number;
  min_stock_level: number;
  is_prescription: boolean;
  is_medical_insurance: boolean;
  storage_condition: string;
  status: 'active' | 'inactive';
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// 药品表单验证规则
export const MEDICINE_VALIDATION_RULES = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: '药品名称为必填项，长度应在2-100个字符之间'
  },
  generic_name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: '通用名为必填项，长度应在2-100个字符之间'
  },
  description: {
    required: false,
    maxLength: 500,
    message: '药品描述长度不能超过500个字符'
  },
  barcode: {
    required: false,
    pattern: /^[0-9]{8,18}$/,
    message: '条形码应为8-18位数字'
  },
  category_id: {
    required: true,
    min: 1,
    message: '请选择药品分类'
  },
  supplier_id: {
    required: false,
    min: 1,
    message: '请选择有效的供应商'
  },
  manufacturer: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: '生产厂家为必填项，长度应在2-100个字符之间'
  },
  approval_number: {
    required: true,
    pattern: /^[A-Z]\d{8}$/,
    message: '批准文号格式不正确，应为1个字母+8个数字（如：H12345678）'
  },
  specification: {
    required: true,
    minLength: 1,
    maxLength: 50,
    message: '规格为必填项，长度不能超过50个字符'
  },
  dosage_form: {
    required: true,
    minLength: 1,
    maxLength: 20,
    message: '剂型为必填项，长度不能超过20个字符'
  },
  price: {
    required: true,
    min: 0.01,
    max: 99999.99,
    message: '销售价格必须大于0.01元且不超过99999.99元'
  },
  cost_price: {
    required: true,
    min: 0.01,
    max: 99999.99,
    message: '成本价格必须大于0.01元且不超过99999.99元'
  },
  stock_quantity: {
    required: true,
    min: 0,
    max: 999999,
    message: '库存数量必须为0-999999之间的整数'
  },
  min_stock_level: {
    required: true,
    min: 0,
    max: 9999,
    message: '最低库存必须为0-9999之间的整数'
  },
  storage_condition: {
    required: true,
    allowedValues: ['常温', '阴凉', '冷藏', '冷冻'],
    message: '请选择正确的储存条件'
  }
};

// 验证单个字段
export function validateField(field: keyof MedicineFormData, value: any): ValidationError | null {
  const rule = MEDICINE_VALIDATION_RULES[field];
  if (!rule) return null;

  // 必填字段验证
  if (rule.required) {
    if (value === undefined || value === null || value === '' || 
        (typeof value === 'number' && value === 0 && field === 'category_id')) {
      return { field, message: rule.message };
    }
  }

  // 如果字段为空且非必填，跳过其他验证
  if (!rule.required && (value === undefined || value === null || value === '')) {
    return null;
  }

  // 字符串长度验证
  if (typeof value === 'string') {
    if (rule.minLength && value.length < rule.minLength) {
      return { field, message: rule.message };
    }
    if (rule.maxLength && value.length > rule.maxLength) {
      return { field, message: rule.message };
    }
  }

  // 数值范围验证
  if (typeof value === 'number') {
    if (rule.min !== undefined && value < rule.min) {
      return { field, message: rule.message };
    }
    if (rule.max !== undefined && value > rule.max) {
      return { field, message: rule.message };
    }
  }

  // 正则表达式验证
  if (rule.pattern && typeof value === 'string') {
    if (!rule.pattern.test(value)) {
      return { field, message: rule.message };
    }
  }

  // 允许值验证
  if (rule.allowedValues && !rule.allowedValues.includes(value)) {
    return { field, message: rule.message };
  }

  return null;
}

// 验证整个表单
export function validateMedicineForm(data: MedicineFormData): ValidationResult {
  const errors: ValidationError[] = [];

  // 验证所有字段
  Object.keys(MEDICINE_VALIDATION_RULES).forEach(field => {
    const error = validateField(field as keyof MedicineFormData, data[field as keyof MedicineFormData]);
    if (error) {
      errors.push(error);
    }
  });

  // 业务逻辑验证
  // 成本价不应高于销售价
  if (data.cost_price > data.price) {
    errors.push({
      field: 'cost_price',
      message: '成本价格不应高于销售价格'
    });
  }

  // 最低库存不应高于当前库存
  if (data.min_stock_level > data.stock_quantity) {
    errors.push({
      field: 'min_stock_level',
      message: '最低库存不应高于当前库存数量'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// 获取字段的友好名称
export function getFieldDisplayName(field: keyof MedicineFormData): string {
  const fieldNames: Record<keyof MedicineFormData, string> = {
    name: '药品名称',
    generic_name: '通用名',
    description: '药品描述',
    barcode: '条形码',
    category_id: '药品分类',
    supplier_id: '供应商',
    manufacturer: '生产厂家',
    approval_number: '批准文号',
    specification: '规格',
    dosage_form: '剂型',
    price: '销售价格',
    cost_price: '成本价格',
    stock_quantity: '库存数量',
    min_stock_level: '最低库存',
    is_prescription: '处方药',
    is_medical_insurance: '医保',
    storage_condition: '储存条件',
    status: '状态'
  };
  
  return fieldNames[field] || field;
}

// 格式化验证错误消息
export function formatValidationErrors(errors: ValidationError[]): string {
  if (errors.length === 0) return '';
  
  if (errors.length === 1) {
    return errors[0].message;
  }
  
  return `表单验证失败，请检查以下字段：\n${errors.map(error => `• ${error.message}`).join('\n')}`;
}
