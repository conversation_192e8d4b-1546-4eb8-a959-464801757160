'use client';

import { useState, useEffect } from 'react';
import StockInForm from './components/StockInForm';
import StockOutForm from './components/StockOutForm';
import BatchManagement from './components/BatchManagement';
import InventoryCount from './components/InventoryCount';
import './styles.css';

// API响应接口
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

interface Product {
  id: number;
  name: string;
  generic_name: string;
  barcode: string;
  stock_quantity: number;
  min_stock_level: number;
  category_name: string;
  manufacturer: string;
  specification: string;
  dosage_form: string;
  storage_condition: string;
  is_prescription: boolean;
  is_medical_insurance: boolean;
  cost_price: number;
}

export default function InventoryPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState<{id: number, name: string}[]>([]);
  const [statusFilter, setStatusFilter] = useState<'all' | 'normal' | 'low' | 'out'>('all');

  // 弹窗状态
  const [isStockInFormOpen, setIsStockInFormOpen] = useState(false);
  const [isStockOutFormOpen, setIsStockOutFormOpen] = useState(false);
  const [isBatchManagementOpen, setIsBatchManagementOpen] = useState(false);
  const [isInventoryCountOpen, setIsInventoryCountOpen] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null);

  // 统计数据
  const [stats, setStats] = useState({
    totalProducts: 0,
    lowStockProducts: 0,
    outOfStockProducts: 0,
    totalStockValue: 0
  });

  // 通知系统
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    show: boolean;
  }>({
    message: '',
    type: 'info',
    show: false
  });

  // 显示通知
  const showNotification = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    setNotification({ message, type, show: true });
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 3000);
  };

  useEffect(() => {
    fetchCategories();
    fetchProducts();
  }, []);

  useEffect(() => {
    // 计算统计数据
    if (products.length > 0) {
      const lowStock = products.filter(p => p.stock_quantity > 0 && p.stock_quantity <= p.min_stock_level).length;
      const outOfStock = products.filter(p => p.stock_quantity === 0).length;

      // 总库存价值计算 (假设每个产品有cost_price字段)
      const totalValue = products.reduce((sum, product) => {
        const price = (product as any).cost_price || 0;
        return sum + (price * product.stock_quantity);
      }, 0);

      setStats({
        totalProducts: products.length,
        lowStockProducts: lowStock,
        outOfStockProducts: outOfStock,
        totalStockValue: totalValue
      });
    }
  }, [products]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      const data = await response.json() as ApiResponse<Product[]>;

      if (data.success) {
        setProducts(data.data || []);
      } else {
        setError(data.message || '获取药品库存数据失败');
      }
    } catch (err) {
      setError('获取药品库存数据时发生错误');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json() as ApiResponse<{id: number, name: string}[]>;
      if (data.success) {
        setCategories(data.data || []);
      }
    } catch (error) {
      console.error('获取分类数据失败:', error);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleViewBatches = (productId: number) => {
    setSelectedProductId(productId);
    setIsBatchManagementOpen(true);
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch =
      (product.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (product.barcode?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (product.manufacturer?.toLowerCase() || '').includes(searchTerm.toLowerCase());

    const matchesCategory =
      !selectedCategory ||
      (product.category_name !== undefined && product.category_name === categories.find(c => c.id.toString() === selectedCategory)?.name);

    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'normal' && product.stock_quantity > product.min_stock_level) ||
      (statusFilter === 'low' && product.stock_quantity > 0 && product.stock_quantity <= product.min_stock_level) ||
      (statusFilter === 'out' && product.stock_quantity === 0);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // 处理库存盘点提交
  const handleInventoryCountSubmit = async (data: any) => {
    try {
      const response = await fetch('/api/inventory/count', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json() as ApiResponse<any>;

      if (result.success) {
        setIsInventoryCountOpen(false);
        fetchProducts(); // 重新加载库存数据
        setError(null);
      } else {
        setError(result.message || '库存盘点操作失败');
      }
    } catch (err) {
      setError('库存盘点操作时发生错误');
      console.error(err);
    }
  };

  // 处理出库提交
  const handleStockOutSubmit = async (data: any) => {
    try {
      const response = await fetch('/api/inventory/stock-out', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json() as ApiResponse<any>;

      if (result.success) {
        setIsStockOutFormOpen(false);
        fetchProducts(); // 重新加载库存数据
      } else {
        setError(result.message || '出库操作失败');
      }
    } catch (err) {
      setError('出库操作时发生错误');
      console.error(err);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-1">总药品数量</h3>
          <p className="text-3xl font-bold text-gray-800">{stats.totalProducts}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-1">库存不足</h3>
          <p className="text-3xl font-bold text-yellow-500">{stats.lowStockProducts}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-1">无库存</h3>
          <p className="text-3xl font-bold text-red-500">{stats.outOfStockProducts}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 mb-1">总库存价值</h3>
          <p className="text-3xl font-bold text-blue-600">¥{stats.totalStockValue.toFixed(2)}</p>
        </div>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold text-gray-800">库存管理</h1>

        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setIsStockInFormOpen(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md flex items-center text-sm"
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            药品入库
          </button>

          <button
            onClick={() => setIsStockOutFormOpen(true)}
            className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded-md flex items-center text-sm"
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            药品出库
          </button>

          <button
            onClick={() => setIsBatchManagementOpen(true)}
            className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-md flex items-center text-sm"
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z"></path>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 10a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            批次管理
          </button>

          <button
            onClick={() => setIsInventoryCountOpen(true)}
            className="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-md flex items-center text-sm"
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
            </svg>
            库存盘点
          </button>
        </div>
      </div>

      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="搜索药品名称、条形码或厂家..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
          />
        </div>
        <div className="w-full md:w-48">
          <select
            value={selectedCategory}
            onChange={handleCategoryFilter}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
          >
            <option value="">全部分类</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
        <div className="w-full md:w-48">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'normal' | 'low' | 'out')}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
          >
            <option value="all">全部状态</option>
            <option value="normal">库存正常</option>
            <option value="low">库存不足</option>
            <option value="out">无库存</option>
          </select>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">药品名称</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">分类</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">规格</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">剂型</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">储存条件</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">当前库存</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">库存状态</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        <div className="text-xs text-gray-500">{product.generic_name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-500">{product.category_name}</td>
                  <td className="px-4 py-3 text-sm text-gray-500">{product.specification}</td>
                  <td className="px-4 py-3 text-sm text-gray-500">{product.dosage_form}</td>
                  <td className="px-4 py-3 text-sm text-gray-500">{product.storage_condition}</td>
                  <td className="px-4 py-3 text-sm font-medium text-gray-900">{product.stock_quantity}</td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        product.stock_quantity > product.min_stock_level
                          ? 'bg-green-100 text-green-800'
                          : product.stock_quantity > 0
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {product.stock_quantity > product.min_stock_level
                        ? '库存充足'
                        : product.stock_quantity > 0
                          ? '库存不足'
                          : '无库存'}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleViewBatches(product.id)}
                        className="text-indigo-600 hover:text-indigo-900 flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2 12s3-8 10-8 10 8 10 8-3 8-10 8-10-8-10-8z"></path>
                        </svg>
                        批次
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
              {filteredProducts.length === 0 && (
                <tr>
                  <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                    没有找到符合条件的药品
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 药品入库表单 */}
      <StockInForm
        isOpen={isStockInFormOpen}
        onClose={() => setIsStockInFormOpen(false)}
        onSubmit={async (formData) => {
          try {
            // 执行入库操作
            const response = await fetch('/api/inventory/stock-in', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(formData),
            });

            const data = await response.json() as ApiResponse<any>;

            if (data.success) {
              // 如果启用了码上放心平台上传且有追溯码，则上传单据
              if (formData.upload_to_mashangfangxin && formData.trace_codes && formData.trace_codes.length > 0) {
                try {
                  console.log('开始上传入库单据到码上放心平台...');

                  const uploadResponse = await fetch('/api/inventory/upload-bill', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      type: 'in',
                      inventory_id: data.data?.inventory_id,
                      trace_codes: formData.trace_codes
                    }),
                  });

                  const uploadResult = await uploadResponse.json();

                  if (uploadResult.success) {
                    console.log('入库单据上传到码上放心平台成功:', uploadResult.data);
                    showNotification(`入库成功！已上传 ${formData.trace_codes.length} 个追溯码到码上放心平台。单据号：${uploadResult.data.bill_code}`, 'success');
                  } else {
                    console.error('上传到码上放心平台失败:', uploadResult.message);
                    showNotification(`入库成功，但上传到码上放心平台失败：${uploadResult.message}`, 'warning');
                  }
                } catch (uploadError) {
                  console.error('上传到码上放心平台时发生错误:', uploadError);
                  showNotification('入库成功，但上传到码上放心平台时发生错误', 'warning');
                }
              } else {
                showNotification('入库成功！', 'success');
              }

              setIsStockInFormOpen(false);
              fetchProducts(); // 重新加载库存数据

              // 返回数据给StockInForm组件处理上传
              return data;
            } else {
              setError(data.message || '入库操作失败');
              throw new Error(data.message || '入库操作失败');
            }
          } catch (err) {
            setError('入库操作时发生错误');
            console.error(err);
            throw err;
          }
        }}
      />

      {/* 药品出库表单 */}
      <StockOutForm
        isOpen={isStockOutFormOpen}
        onClose={() => setIsStockOutFormOpen(false)}
        onSubmit={handleStockOutSubmit}
      />

      {/* 批次管理 */}
      <BatchManagement
        isOpen={isBatchManagementOpen}
        onClose={() => {
          setIsBatchManagementOpen(false);
          setSelectedProductId(null);
        }}
        productId={selectedProductId || undefined}
      />

      {/* 库存盘点 */}
      <InventoryCount
        isOpen={isInventoryCountOpen}
        onClose={() => setIsInventoryCountOpen(false)}
        onSubmit={handleInventoryCountSubmit}
      />

      {/* 通知组件 - 动态大小调整 */}
      {notification.show && (
        <div className="fixed top-4 right-4 z-50 max-w-sm sm:max-w-md lg:max-w-lg">
          <div className={`p-4 rounded-lg shadow-lg border-l-4 min-w-80 max-w-full ${
            notification.type === 'success' ? 'bg-green-50 border-green-500 text-green-800' :
            notification.type === 'error' ? 'bg-red-50 border-red-500 text-red-800' :
            notification.type === 'warning' ? 'bg-yellow-50 border-yellow-500 text-yellow-800' :
            'bg-blue-50 border-blue-500 text-blue-800'
          }`}>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {notification.type === 'success' && <span className="text-green-500">✅</span>}
                {notification.type === 'error' && <span className="text-red-500">❌</span>}
                {notification.type === 'warning' && <span className="text-yellow-500">⚠️</span>}
                {notification.type === 'info' && <span className="text-blue-500">ℹ️</span>}
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-sm font-medium whitespace-pre-line break-words">{notification.message}</p>
              </div>
              <button
                onClick={() => setNotification(prev => ({ ...prev, show: false }))}
                className="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}