# 任务开发流程规范

## 概述

本文档描述了药店零售系统项目中复杂功能开发的标准流程，强调使用任务管理工具进行结构化开发，确保开发过程的可追踪性和高质量交付。

## 核心原则

### 1. 任务驱动开发
- 所有复杂功能开发必须先创建详细的任务列表
- 任务分解应该达到合适的粒度（每个子任务约20分钟完成）
- 通过任务状态跟踪开发进度

### 2. 结构化规划
- 在编写代码前必须完成充分的信息收集和分析
- 制定详细的实施计划和技术方案
- 识别潜在风险和依赖关系

### 3. 持续反馈
- 定期更新任务状态和进度
- 及时记录遇到的问题和解决方案
- 保持与用户的沟通和反馈循环

## 标准开发流程

### 阶段1: 需求分析和信息收集

#### 1.1 需求理解
- **目标**: 深入理解用户需求和业务场景
- **活动**:
  - 分析用户提供的需求描述
  - 识别核心功能点和边界条件
  - 确定验收标准和成功指标

#### 1.2 技术调研
- **目标**: 收集实现所需的技术信息
- **活动**:
  - 使用`codebase-retrieval`工具分析相关代码
  - 了解现有组件和API的实现方式
  - 识别可复用的代码和组件

#### 1.3 依赖分析
- **目标**: 识别功能实现的依赖关系
- **活动**:
  - 分析数据库结构和API接口
  - 确定需要修改的文件和组件
  - 识别可能影响的其他功能模块

### 阶段2: 任务规划和分解

#### 2.1 创建主任务
使用`add_tasks`工具创建主要任务：
```
任务名称: 简洁明确的功能描述
任务描述: 详细的实现要求和验收标准
```

#### 2.2 任务分解
将主任务分解为具体的子任务：
- **粒度标准**: 每个子任务预计完成时间20-30分钟
- **依赖关系**: 明确任务间的先后顺序
- **可测试性**: 每个任务完成后都应该可以验证

#### 2.3 任务分类
按照以下类型对任务进行分类：
- **分析类**: 代码分析、需求澄清、技术调研
- **开发类**: 组件创建、功能实现、API开发
- **测试类**: 功能测试、集成测试、用户验证
- **文档类**: 代码注释、使用说明、技术文档

### 阶段3: 开发实施

#### 3.1 任务状态管理
使用标准的任务状态：
- `NOT_STARTED` [ ]: 尚未开始的任务
- `IN_PROGRESS` [/]: 正在进行的任务
- `COMPLETE` [x]: 已完成的任务
- `CANCELLED` [-]: 已取消的任务

#### 3.2 开发规范
- **代码质量**: 遵循项目的编码规范和最佳实践
- **组件复用**: 优先使用现有组件，避免重复开发
- **错误处理**: 实现完善的错误处理和用户反馈
- **性能考虑**: 注意代码性能和用户体验

#### 3.3 进度跟踪
- 及时更新任务状态：`update_tasks`
- 记录遇到的问题和解决方案
- 定期与用户沟通进度和反馈

### 阶段4: 测试和验证

#### 4.1 功能测试
- 验证每个功能点是否按预期工作
- 测试边界条件和异常情况
- 确保与现有功能的兼容性

#### 4.2 用户体验测试
- 验证UI/UX的一致性
- 测试响应式设计和移动端适配
- 确保符合项目的设计规范

#### 4.3 集成测试
- 测试与其他模块的集成
- 验证数据流和API调用
- 确保系统整体稳定性

## 任务管理工具使用指南

### 基本操作

#### 创建任务
```typescript
add_tasks([{
  name: "任务名称",
  description: "详细描述",
  parent_task_id: "父任务ID（可选）",
  after_task_id: "前置任务ID（可选）"
}])
```

#### 更新任务状态
```typescript
// 单个任务更新
update_tasks([{
  task_id: "任务ID",
  state: "COMPLETE"
}])

// 批量更新（推荐）
update_tasks([
  { task_id: "task1", state: "COMPLETE" },
  { task_id: "task2", state: "IN_PROGRESS" }
])
```

#### 查看任务列表
```typescript
view_tasklist()
```

### 最佳实践

#### 1. 任务命名规范
- 使用动词开头：创建、修改、优化、测试
- 包含具体的对象：页面、组件、功能
- 示例：
  - ✅ "创建删除确认对话框组件"
  - ✅ "修改药品管理页面删除功能"
  - ❌ "弹窗功能"
  - ❌ "修改代码"

#### 2. 任务描述规范
- 明确说明要做什么
- 包含验收标准
- 提及相关的技术要求
- 示例：
```
描述: 移除药品管理页面中的浏览器原生confirm()弹窗，
改为使用自定义删除确认模态对话框，保持现有删除逻辑不变。
验收标准: 
- 删除按钮点击后显示自定义确认对话框
- 确认删除后正常执行删除操作
- 取消操作正常关闭对话框
- 保持蓝色字体主题一致性
```

#### 3. 任务状态更新
- 开始工作时立即更新为IN_PROGRESS
- 完成后及时更新为COMPLETE
- 使用批量更新提高效率
- 如果任务需要调整，及时更新描述

## 质量保证

### 代码审查检查点
- [ ] 代码符合项目规范
- [ ] 实现了所有需求功能
- [ ] 错误处理完善
- [ ] 用户体验良好
- [ ] 性能表现acceptable
- [ ] 测试覆盖充分

### 交付标准
- [ ] 所有任务状态为COMPLETE
- [ ] 功能测试通过
- [ ] 用户验收通过
- [ ] 代码已提交
- [ ] 文档已更新

## 常见问题和解决方案

### Q1: 任务分解过细或过粗怎么办？
**A**: 
- 过细：合并相关的小任务
- 过粗：进一步分解为可独立完成的子任务
- 标准：每个任务20-30分钟完成

### Q2: 开发过程中发现新的需求怎么办？
**A**:
- 立即与用户沟通确认
- 创建新的任务或修改现有任务
- 更新项目计划和时间估算

### Q3: 任务依赖关系复杂怎么处理？
**A**:
- 使用parent_task_id创建任务层次
- 使用after_task_id指定执行顺序
- 必要时重新组织任务结构

### Q4: 如何处理阻塞任务？
**A**:
- 及时识别和报告阻塞问题
- 寻找替代方案或并行任务
- 与相关方协调解决依赖问题

## 工具和资源

### 开发工具
- `codebase-retrieval`: 代码分析和信息收集
- `view`: 查看文件和目录结构
- `str-replace-editor`: 代码编辑和修改
- `save-file`: 创建新文件

### 任务管理工具
- `add_tasks`: 创建新任务
- `update_tasks`: 更新任务状态
- `view_tasklist`: 查看任务列表
- `reorganize_tasklist`: 重组任务结构

### 测试工具
- `launch-process`: 启动开发服务器
- `open-browser`: 打开浏览器测试
- `diagnostics`: 检查代码问题

## 更新记录

- **2025-07-05**: 初始版本创建
- 建立了标准的任务驱动开发流程
- 定义了任务管理工具的使用规范
- 提供了质量保证和问题解决指南
