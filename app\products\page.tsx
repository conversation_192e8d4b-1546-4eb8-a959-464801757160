'use client';

import { useState, useEffect } from 'react';
import MedicineForm, { MedicineFormData } from './components/MedicineForm';
import BarcodeScanner from './components/BarcodeScanner';
import DrugTraceInfo from './components/DrugTraceInfo';
import ErrorDialog from './components/ErrorDialog';
import ConfirmDeleteDialog from '../components/ConfirmDeleteDialog';
import React from 'react';

interface Product {
  id: number;
  name: string;
  generic_name: string;
  description: string;
  barcode: string;
  category_id: number;
  category_name: string;
  manufacturer: string;
  approval_number: string;
  specification: string;
  dosage_form: string;
  price: number;
  cost_price: number;
  stock_quantity: number;
  min_stock_level: number;
  is_prescription: boolean;
  is_medical_insurance: boolean;
  storage_condition: string;
  status: 'active' | 'inactive';
}

export default function MedicinesPage() {
  const [medicines, setMedicines] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [isMedicineFormOpen, setIsMedicineFormOpen] = useState(false);
  const [editingMedicine, setEditingMedicine] = useState<Product | null>(null);
  const [formInitialData, setFormInitialData] = useState<MedicineFormData | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState<{id: number, name: string}[]>([]);

  // 扫码相关状态
  const [isScannerActive, setIsScannerActive] = useState(false);
  const [scannedCode, setScannedCode] = useState<string | null>(null);
  const [isDrugTraceInfoOpen, setIsDrugTraceInfoOpen] = useState(false);
  const [drugTraceInfo, setDrugTraceInfo] = useState<any>(null);
  const [codeType, setCodeType] = useState<'traceCode' | 'barcode' | 'supervisionCode' | 'deviceUDI' | null>(null);

  // 错误弹窗状态
  const [isErrorDialogOpen, setIsErrorDialogOpen] = useState(false);
  const [errorDialogMessage, setErrorDialogMessage] = useState('');

  // 删除确认对话框状态
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [deletingMedicine, setDeletingMedicine] = useState<Product | null>(null);

  useEffect(() => {
    fetchMedicines();
    fetchCategories();
  }, []);

  const fetchMedicines = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json() as { success: boolean; data?: Product[]; message?: string };

      if (data.success) {
        setMedicines(data.data || []);
      } else {
        // 使用弹窗显示错误信息
        setErrorDialogMessage(data.message || '获取药品数据失败');
        setIsErrorDialogOpen(true);
      }
    } catch (err) {
      // 使用弹窗显示错误信息
      setErrorDialogMessage('获取药品数据时发生错误');
      setIsErrorDialogOpen(true);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json() as { success: boolean; data?: { id: number, name: string }[]; message?: string };
      if (data.success) {
        setCategories(data.data || []);
      }
    } catch (error) {
      console.error('获取分类数据失败:', error);
    }
  };

  // 显示删除确认对话框
  const handleDeleteClick = (medicine: Product) => {
    setDeletingMedicine(medicine);
    setIsDeleteConfirmOpen(true);
  };

  // 执行删除操作
  const handleDeleteConfirm = async () => {
    if (!deletingMedicine) return;

    try {
      const response = await fetch(`/api/products?id=${deletingMedicine.id}`, {
        method: 'DELETE',
      });

      const data = await response.json() as { success: boolean; message?: string };

      if (data.success) {
        setMedicines(medicines.filter(medicine => medicine.id !== deletingMedicine.id));
      } else {
        // 使用弹窗显示错误信息
        setErrorDialogMessage(data.message || '删除药品失败');
        setIsErrorDialogOpen(true);
      }
    } catch (err) {
      // 使用弹窗显示错误信息
      setErrorDialogMessage('删除药品时发生错误');
      setIsErrorDialogOpen(true);
      console.error(err);
    } finally {
      setDeletingMedicine(null);
    }
  };

  const handleEdit = (medicine: Product) => {
    setEditingMedicine({...medicine});
    setIsMedicineFormOpen(true);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    // @ts-ignore
    setSearchTerm(e.target.value);
  };

  const handleCategoryFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    // @ts-ignore
    setSelectedCategory(e.target.value);
  };

  // 处理扫码结果
  const handleScan = async (code: string) => {
    setScannedCode(code);

    try {
      // 先识别条码类型
      const typeResponse = await fetch(`/api/mashangfangxin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'identifyCodeType',
          params: { code }
        }),
      });

      if (!typeResponse.ok) {
        setErrorDialogMessage('识别条码类型失败，请重试');
        setIsErrorDialogOpen(true);
        return;
      }

      const typeData = await typeResponse.json();
      if (!typeData.success) {
        setErrorDialogMessage('识别条码类型失败: ' + typeData.message);
        setIsErrorDialogOpen(true);
        return;
      }

      const codeType = typeData.data;
      console.log('识别到的条码类型:', codeType);

      // 判断是否是药品追溯码
      if (codeType !== 'traceCode') {
        // 如果不是药品追溯码，显示提示信息
        if (codeType === 'barcode') {
          setErrorDialogMessage('扫描的是商品条形码，不是药品追溯码。请扫描药品追溯码以获取药品信息。');
        } else if (codeType === 'supervisionCode') {
          setErrorDialogMessage('扫描的是药品监管码，不是药品追溯码。请扫描药品追溯码以获取药品信息。');
        } else if (codeType === 'deviceUDI') {
          setErrorDialogMessage('扫描的是医疗器械UDI，不是药品追溯码。请扫描药品追溯码以获取药品信息。');
        } else {
          setErrorDialogMessage('扫描的不是药品追溯码。请扫描药品追溯码以获取药品信息。');
        }
        setIsErrorDialogOpen(true);
        setIsScannerActive(false);
        return;
      }

      // 如果是药品追溯码，继续调用API获取药品追溯信息
      const response = await fetch(`/api/drug-trace?code=${encodeURIComponent(code)}`);
      const data = await response.json();

      console.log('药品追溯信息API响应:', JSON.stringify(data, null, 2));

      if (data.success) {
        console.log('药品追溯信息获取成功，药品信息:', JSON.stringify(data.data.drugInfo, null, 2));
        console.log('条码类型:', data.data.codeType);

        setDrugTraceInfo(data.data.drugInfo);
        setCodeType(data.data.codeType);
        setIsDrugTraceInfoOpen(true);
      } else {
        // 显示错误弹窗
        let errorMessage = '获取药品追溯信息失败: ' + data.message;

        // 检查是否是企业ID未设置的错误
        if (data.message && data.message.includes('企业ID未设置')) {
          errorMessage = '企业ID未设置，请在系统设置中配置码上放心开放平台企业ID后再试';
        }

        setErrorDialogMessage(errorMessage);
        setIsErrorDialogOpen(true);
      }
    } catch (error) {
      console.error('扫码处理失败:', error);
      // 显示错误弹窗
      setErrorDialogMessage('扫码处理失败，请重试');
      setIsErrorDialogOpen(true);
    } finally {
      setIsScannerActive(false);
    }
  };

  // 处理添加扫码药品到系统
  const handleAddScannedProduct = (drugInfo: any) => {
    // 将药品追溯信息转换为药品表单数据格式
    let newProduct: MedicineFormData;

    console.log('处理药品信息:', JSON.stringify(drugInfo, null, 2));

    // 获取商品条形码（如果有）
    const barcode = drugInfo.barcode || '';



    // 尝试从新的API响应格式中提取药品信息
    if (drugInfo.result?.models?.code_full_info_dto?.[0]) {
      // 新的API响应格式
      const codeFullInfo = drugInfo.result.models.code_full_info_dto[0];
      const drugEntBase = codeFullInfo.drug_ent_base_d_t_o || {};
      const manufacturer = codeFullInfo.p_user_ent_d_t_o?.ent_name || '';

      newProduct = {
        name: drugEntBase.physic_name || '',
        generic_name: drugEntBase.physic_name || '',
        description: '',
        barcode: barcode,

        category_id: 0,
        manufacturer: manufacturer,
        approval_number: drugEntBase.approval_licence_no || '',
        specification: drugEntBase.pkg_spec_crit || '',
        dosage_form: drugEntBase.prepn_type_desc || '',
        price: 0,
        cost_price: 0,
        stock_quantity: 0,
        min_stock_level: 5,
        is_prescription: false,
        is_medical_insurance: false,
        storage_condition: '常温',
        status: 'active'
      };
    } else {
      // 旧的API响应格式或未知格式，尝试提取基本信息
      newProduct = {
        name: drugInfo.drug_name || '',
        generic_name: drugInfo.drug_name || '',
        description: '',
        barcode: barcode,

        category_id: 0,
        manufacturer: drugInfo.manufacturer || '',
        approval_number: drugInfo.approval_number || '',
        specification: drugInfo.specification || '',
        dosage_form: drugInfo.dosage_form || '',
        price: 0,
        cost_price: 0,
        stock_quantity: 0,
        min_stock_level: 5,
        is_prescription: false,
        is_medical_insurance: false,
        storage_condition: '常温',
        status: 'active'
      };
    }

    // 打开药品表单，预填充数据，但不设置为编辑模式
    if (newProduct) {
      // 使用initialData预填充表单，但不设置editingMedicine，这样就不会进入编辑模式
      setEditingMedicine(null); // 确保不是编辑模式
      // 使用一个临时状态来存储预填充数据
      setFormInitialData(newProduct);
      setIsMedicineFormOpen(true);
    }
  };

  const filteredMedicines = medicines.filter(medicine => {
    const matchesSearch =
      (medicine.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (medicine.barcode?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (medicine.manufacturer?.toLowerCase() || '').includes(searchTerm.toLowerCase());

    const matchesCategory =
      !selectedCategory ||
      (medicine.category_id !== undefined && medicine.category_id.toString() === selectedCategory);

    return matchesSearch && matchesCategory;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // 不再使用左上角错误显示，改为完全使用弹窗

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">药品管理</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => setIsScannerActive(!isScannerActive)}
            className={`${
              isScannerActive
                ? 'bg-yellow-500 hover:bg-yellow-600'
                : 'bg-green-500 hover:bg-green-600'
            } text-white px-5 py-2 rounded-md shadow-sm transition-colors duration-200 flex items-center`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            {isScannerActive ? '取消扫码' : '扫描条码'}
          </button>
          <button
            onClick={() => {
              setEditingMedicine(null);
              setIsMedicineFormOpen(true);
            }}
            className="bg-blue-500 hover:bg-blue-600 text-white px-5 py-2 rounded-md shadow-sm transition-colors duration-200"
          >
            添加药品
          </button>
        </div>
      </div>

      <div className="mb-6 flex space-x-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="搜索药品名称、条形码或生产厂家..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
          />
        </div>
        <div className="w-48">
          <select
            value={selectedCategory}
            onChange={handleCategoryFilter}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
          >
            <option value="">全部分类</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 条码扫描组件 */}
      <BarcodeScanner
        onScan={handleScan}
        isActive={isScannerActive}
      />

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 table-fixed" style={{ minWidth: '1500px' }}>
            <thead className="bg-gray-50">
              <tr key="header-row">
                <th scope="col" className="w-48 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">药品名称</th>
                <th scope="col" className="w-40 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">通用名</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">分类</th>
                <th scope="col" className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">条形码</th>
                <th scope="col" className="w-48 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">生产厂家</th>
                <th scope="col" className="w-48 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">批准文号</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">规格</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">剂型</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">售价</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">库存</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">最低库存</th>
                <th scope="col" className="w-32 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">储存条件</th>
                <th scope="col" className="w-20 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">处方药</th>
                <th scope="col" className="w-20 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">医保</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">状态</th>
                <th scope="col" className="w-24 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200" key="medicines-tbody">
              {filteredMedicines.map((medicine) => (
                <tr key={medicine.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{medicine.name}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.generic_name}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.category_name}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.barcode}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.manufacturer}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.approval_number}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.specification}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.dosage_form}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">¥{medicine.price !== undefined ? medicine.price.toFixed(2) : '0.00'}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.stock_quantity !== undefined ? medicine.stock_quantity : 0}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.min_stock_level !== undefined ? medicine.min_stock_level : 0}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.storage_condition || '常温'}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">{medicine.is_prescription ? '是' : '否'}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 text-center">{medicine.is_medical_insurance ? '是' : '否'}</td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span
                      key={`status-${medicine.id}`}
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      medicine.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {medicine.status === 'active' ? '在售' : '下架'}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(medicine)}
                        className="text-blue-600 hover:text-blue-900"
                        key={`edit-${medicine.id}`}
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => handleDeleteClick(medicine)}
                        className="text-red-600 hover:text-red-900"
                        key={`delete-${medicine.id}`}
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 药品表单 */}
      <MedicineForm
        isOpen={isMedicineFormOpen}
        onClose={() => {
          setIsMedicineFormOpen(false);
          setEditingMedicine(null);
          setFormInitialData(null);
        }}
        onSubmit={async (data) => {
          try {
            // 确保category_id是数字类型
            const formattedData = {
              ...data,
              category_id: typeof data.category_id === 'string' ? parseInt(data.category_id, 10) : data.category_id
            };

            // 检查category_id是否有效
            if (!formattedData.category_id) {
              setErrorDialogMessage('请选择药品分类');
              setIsErrorDialogOpen(true);
              return;
            }

            // 明确区分编辑模式和添加模式
            // 只有当editingMedicine有值且有id属性时才是编辑模式
            const isEditing = !!editingMedicine && 'id' in editingMedicine;

            // 强制使用POST方法添加新药品
            const method = isEditing ? 'PUT' : 'POST';

            console.log('提交的数据:', formattedData);
            console.log('使用的方法:', method);
            console.log('是否编辑模式:', isEditing);
            console.log('editingMedicine:', editingMedicine);

            // 准备请求体，只有在编辑模式下才添加id
            const requestBody = isEditing && editingMedicine?.id
              ? { ...formattedData, id: editingMedicine.id }
              : formattedData;

            console.log('请求体:', JSON.stringify(requestBody, null, 2));

            const response = await fetch('/api/products', {
              method: method,
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(requestBody),
            });

            const dataResponse = await response.json() as { success: boolean; message?: string; error?: string };
            console.log('API响应:', dataResponse);

            if (dataResponse.success) {
              setIsMedicineFormOpen(false);
              setEditingMedicine(null);
              setFormInitialData(null);
              fetchMedicines(); // 重新加载药品数据
            } else {
              // 使用弹窗显示错误信息，包含更详细的错误信息
              const errorMsg = dataResponse.message || (isEditing ? '更新药品失败' : '添加药品失败');
              const detailMsg = dataResponse.error ? `\n详细错误: ${dataResponse.error}` : '';
              setErrorDialogMessage(errorMsg + detailMsg);
              setIsErrorDialogOpen(true);
            }
          } catch (err) {
            // 使用弹窗显示错误信息
            console.error('提交数据时发生错误:', err);
            setErrorDialogMessage('提交数据时发生错误: ' + (err instanceof Error ? err.message : String(err)));
            setIsErrorDialogOpen(true);
          }
        }}
        initialData={editingMedicine || formInitialData}
        title={editingMedicine && 'id' in editingMedicine ? '编辑药品' : '添加药品'}
      />

      {/* 药品追溯信息弹窗 */}
      <DrugTraceInfo
        isOpen={isDrugTraceInfoOpen}
        onClose={() => {
          setIsDrugTraceInfoOpen(false);
          setDrugTraceInfo(null);
          setCodeType(null);
          setScannedCode(null);
        }}
        drugInfo={drugTraceInfo}
        codeType={codeType}
        code={scannedCode || ''}
        onAddProduct={handleAddScannedProduct}
      />

      {/* 错误提示弹窗 */}
      <ErrorDialog
        isOpen={isErrorDialogOpen}
        onClose={() => setIsErrorDialogOpen(false)}
        errorMessage={errorDialogMessage}
      />

      {/* 删除确认对话框 */}
      <ConfirmDeleteDialog
        isOpen={isDeleteConfirmOpen}
        onClose={() => {
          setIsDeleteConfirmOpen(false);
          setDeletingMedicine(null);
        }}
        onConfirm={handleDeleteConfirm}
        title="确认删除药品"
        message="确定要删除这个药品吗？"
        itemName={deletingMedicine?.name}
      />
    </div>
  );
}