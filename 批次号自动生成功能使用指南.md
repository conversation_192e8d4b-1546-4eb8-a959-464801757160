# 批次号自动生成功能使用指南

## 功能概述

本系统现已支持智能批次号管理功能，包括：
- ✅ 自动生成批次号（推荐）
- ✅ 手动输入批次号
- ✅ 多种批次类型支持
- ✅ 入库和出库批次号管理
- ✅ 批次号格式验证

## 批次号格式规范

### 系统自动生成格式
**格式：** `类型前缀-YYYYMMDD序号`

**示例：**
- `PC-202506290001` - 2025年6月29日第1个采购批次
- `PR-202506290002` - 2025年6月29日第2个生产批次
- `SO-202506290001` - 2025年6月29日第1个销售出库批次

### 支持的批次类型

#### 入库批次类型
| 代码 | 中文名称 | 用途 |
|------|---------|------|
| PC | 采购批次 | 从供应商采购的药品 |
| PR | 生产批次 | 自主生产的药品 |
| RT | 退货批次 | 退货处理的药品 |
| AD | 调整批次 | 库存调整的药品 |
| TR | 调拨批次 | 门店间调拨的药品 |
| QC | 质检批次 | 质量检验的药品 |

#### 出库批次类型
| 代码 | 中文名称 | 用途 |
|------|---------|------|
| SO | 销售出库 | 销售给客户 |
| RT | 退货出库 | 退货给供应商 |
| EX | 过期处理 | 过期药品处理 |
| DM | 破损处理 | 破损药品处理 |
| OT | 其他原因 | 其他出库原因 |

## 使用方法

### 1. 药品入库时的批次号管理

#### 步骤1：选择批次号输入方式
在入库表单中，您会看到两个选项：
- **自动生成**（推荐）：系统自动生成标准格式的批次号
- **手动输入**：手动输入自定义批次号

#### 步骤2：自动生成批次号
1. 选择"自动生成"选项
2. 选择批次类型（默认为"采购批次"）
3. 选择药品后，点击"生成"按钮
4. 系统自动生成格式化的批次号

#### 步骤3：手动输入批次号
1. 选择"手动输入"选项
2. 在文本框中输入批次号
3. 系统会自动验证格式是否正确

### 2. 药品出库时的批次号管理

出库时系统会根据出库原因自动生成相应的批次号：

#### 自动批次号生成规则
- **销售出库** → `SO-日期序号`
- **退货出库** → `RT-日期序号`
- **过期处理** → `EX-日期序号`
- **破损处理** → `DM-日期序号`
- **其他原因** → `OT-日期序号`

#### 操作步骤
1. 选择药品
2. 选择出库原因
3. 系统自动生成对应类型的批次号
4. 完成出库操作

## API接口说明

### 1. 生成批次号API
```
GET /api/inventory/batch-number?type=PC&product_id=7
```

**参数：**
- `type`: 批次类型（PC、PR、RT等）
- `product_id`: 药品ID（可选）

**响应示例：**
```json
{
  "success": true,
  "data": {
    "batchNumber": "PC-202506290001",
    "sequence": 1,
    "dateStr": "20250629",
    "batchType": "PC",
    "productInfo": {
      "编号": 7,
      "名称": "西瓜霜润喉片",
      "规格": "36片/盒"
    },
    "suggestion": {
      "description": "采购批次 (Purchase)",
      "format": "PC-YYYYMMDD0001",
      "example": "PC-202506290001"
    }
  }
}
```

### 2. 验证批次号API
```
POST /api/inventory/batch-number
Content-Type: application/json

{
  "batchNumber": "PC-202506290001",
  "productId": 7
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "batchNumber": "PC-202506290001",
    "isValid": true,
    "isDuplicate": false,
    "format": "system",
    "type": "PC",
    "date": "20250629",
    "sequence": "0001"
  }
}
```

### 3. 入库API（支持自动生成批次号）
```
POST /api/inventory/stock-in
Content-Type: application/json

{
  "product_id": 7,
  "quantity": 10,
  "auto_generate_batch": true,
  "batch_type": "PC",
  "supplier_id": 1,
  "stock_in_date": "2025-06-29",
  "expiry_date": "2027-03-26",
  "cost_price": 15.00,
  "notes": "自动生成批次号测试",
  "trace_codes": [],
  "upload_to_mashangfangxin": false
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "入库成功",
  "data": {
    "inventory_id": 7,
    "product_id": 7,
    "product_name": "西瓜霜润喉片",
    "quantity": 10,
    "batch_number": "PC-202506290002",
    "batch_number_source": "auto",
    "previous_stock": 33,
    "new_stock": 43,
    "trace_codes_count": 0,
    "upload_to_mashangfangxin": false
  }
}
```

## 功能优势

### 1. 标准化管理
- 统一的批次号格式，便于管理和查询
- 包含日期信息，便于追溯
- 自动递增序号，避免重复

### 2. 业务场景支持
- 支持多种入库和出库场景
- 根据业务类型自动选择批次前缀
- 灵活的手动输入选项

### 3. 数据完整性
- 自动验证批次号格式
- 检查重复批次号
- 完整的错误处理机制

### 4. 用户体验
- 简单易用的界面
- 自动生成减少输入错误
- 实时反馈和提示

## 测试验证

### 功能测试结果
✅ 批次号生成API正常工作  
✅ 入库自动生成批次号功能正常  
✅ 出库自动生成批次号功能正常  
✅ 手动输入批次号验证正常  
✅ 多种批次类型支持正常  

### 测试数据示例
- 生成的采购批次号：`PC-202506290002`
- 生成的销售出库批次号：`SO-202506290001`
- 库存正确更新，数据一致性良好

## 注意事项

1. **选择药品后再生成批次号**：确保先选择药品，再点击生成批次号按钮
2. **批次号唯一性**：系统会自动确保同一天内的批次号不重复
3. **格式验证**：手动输入的批次号会进行格式验证
4. **数据备份**：重要的批次号数据建议定期备份

## 后续扩展

计划中的功能扩展：
- 批次号查询和统计功能
- 批次号流向追溯报表
- 批次号有效期预警
- 自定义批次号格式设置

---

**系统版本：** v1.0  
**更新日期：** 2025-06-29  
**功能状态：** ✅ 已完成并测试通过
